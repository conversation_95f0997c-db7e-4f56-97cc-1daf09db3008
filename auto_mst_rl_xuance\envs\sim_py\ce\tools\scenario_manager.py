"""
场景管理器模块

负责生成、保存、加载和供给用于强化学习训练和评估的固定场景。
"""

import os
import pickle
from copy import deepcopy
from typing import Dict, List

import numpy as np

# 从当前项目的其他模块导入必要的类和函数
from .agent_utils import Agent
from .init_simulation import create_initial_runtime_state, init_sim_robots


class ScenarioManager:
    """
    管理固定的训练和评估场景
    """

    def __init__(
        self,
        save_path: str,
        num_train_scenarios: int,
        num_eval_scenarios: int,
        runtime_config: object,
    ):
        """
        初始化场景管理器

        Args:
            save_path (str): 场景文件的保存路径 (.pkl)
            num_train_scenarios (int): 要生成/加载的训练场景数量
            num_eval_scenarios (int): 用于评估的场景数量 (从训练场景中选取)
            runtime_config (object): 包含仿真配置的对象 (通常是configs.args)
        """
        self.save_path = save_path
        self.num_train_scenarios = num_train_scenarios
        self.num_eval_scenarios = num_eval_scenarios
        self.runtime_config = runtime_config
        self.scenarios: List[Dict[int, Agent]] = []

        # 随机化初始索引以增加并行训练时的多样性
        # 增加检查以避免在数量为0时出错
        self._train_scenario_idx = (
            np.random.randint(0, self.num_train_scenarios)
            if self.num_train_scenarios > 0
            else 0
        )
        self._eval_scenario_idx = (
            np.random.randint(0, self.num_eval_scenarios)
            if self.num_eval_scenarios > 0
            else 0
        )

        # 加载或生成场景
        if os.path.exists(self.save_path):
            print(f"INFO: 从 '{self.save_path}' 加载场景...")
            self._load_scenarios()
        else:
            print(f"INFO: 场景文件 '{self.save_path}' 不存在，开始生成新场景...")
            self._generate_and_save_scenarios()

    def _generate_and_save_scenarios(self):
        """
        生成并保存场景文件
        """
        print(
            f"开始生成 {self.num_train_scenarios} 个训练场景... 这个过程可能需要一些时间。"
        )
        # 确保保存目录存在
        os.makedirs(os.path.dirname(self.save_path), exist_ok=True)

        generated_scenarios = []
        for i in range(self.num_train_scenarios):
            # 1. 创建一个临时的运行时状态
            #    现在直接传递完整的config对象
            temp_runtime_state = create_initial_runtime_state(self.runtime_config)

            # 2. 调用现有的机器人初始化函数
            initialized_state = init_sim_robots(
                temp_runtime_state,
                "unif",
                np.array([0, 0]),
                100,
                True,  # is_parallel_simulation
            )

            # 3. 只保存核心的 actors 字典
            generated_scenarios.append(deepcopy(initialized_state.actors))

            if (i + 1) % 10 == 0:
                print(f"  ...已生成 {i + 1}/{self.num_train_scenarios} 个场景")

        self.scenarios = generated_scenarios

        # 4. 保存到文件
        with open(self.save_path, "wb") as f:
            pickle.dump(self.scenarios, f)

        print(f"✓ 场景已成功生成并保存到 '{self.save_path}'")

    def _load_scenarios(self):
        """
        从文件加载场景
        """
        with open(self.save_path, "rb") as f:
            self.scenarios = pickle.load(f)

        # 验证加载的场景数量是否足够
        if len(self.scenarios) < self.num_train_scenarios:
            print(
                f"WARNING: 加载的场景数量 ({len(self.scenarios)}) 少于需求的数量 ({self.num_train_scenarios})。"
            )
            print("将重新生成所有场景。")
            self._generate_and_save_scenarios()
        else:
            print(
                f"✓ 成功加载 {len(self.scenarios)} 个场景。训练将使用 {self.num_train_scenarios} 个，评估将使用前 {self.num_eval_scenarios} 个。"
            )

    def get_training_scenario(self) -> Dict[int, Agent]:
        """
        循环获取一个训练场景

        Returns:
            一个场景 (actors字典的深拷贝)
        """
        scenario = self.scenarios[self._train_scenario_idx]
        self._train_scenario_idx = (
            self._train_scenario_idx + 1
        ) % self.num_train_scenarios
        return deepcopy(scenario)

    def get_evaluation_scenario(self) -> Dict[int, Agent]:
        """
        循环获取一个评估场景 (从训练场景的前N个中选取)

        Returns:
            一个场景 (actors字典的深拷贝)
        """
        scenario = self.scenarios[self._eval_scenario_idx]
        print(f"DEBUG: 提供评估场景 #{self._eval_scenario_idx}")
        self._eval_scenario_idx = (
            self._eval_scenario_idx + 1
        ) % self.num_eval_scenarios
        return deepcopy(scenario)
