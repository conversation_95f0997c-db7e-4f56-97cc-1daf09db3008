"""
验证评估场景管理机制的测试脚本

该脚本验证 train_mst_ce.py 中评估过程的场景管理机制是否按预期工作，
核心是验证评估的确定性和一致性。

验证点：
1. 场景数量：验证每次评估是否都加载了恰好10个场景
2. 场景固定性：验证每次评估加载的场景序列是否完全相同
3. 评估一致性：验证在使用相同神经网络权重时，多次独立评估的最终得分是否完全一致

使用方法：
1. 完整测试：python verify_eval_scenario.py
2. 快速测试：python verify_eval_scenario.py --quick

注意事项：
- 确保 scenarios/ce_scenarios.pkl 文件存在
- 脚本会自动创建和清理临时模型文件
- 测试过程中会使用猴子补丁技术追踪场景加载
"""

import argparse
import hashlib
import os
import sys
import tempfile
from copy import deepcopy

import numpy as np
import yaml

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, ".."))
sys.path.insert(0, project_root)

# 导入必要的模块
from auto_mst_rl_xuance.envs import register_envs
from xuance.environment import make_envs
from xuance.torch.agents import MAPPO_Agents
from xuance.torch.utils.operations import set_seed


def load_config(file_path):
    """加载YAML配置文件，指定UTF-8编码"""
    with open(file_path, "r", encoding="utf-8") as f:
        config_dict = yaml.load(f, Loader=yaml.FullLoader)
    return config_dict


def compute_scenario_hash(actors_dict):
    """
    计算场景的哈希值，基于所有智能体的初始位置

    Args:
        actors_dict: 包含所有智能体的字典

    Returns:
        str: 场景的哈希值
    """
    # 提取所有智能体的位置信息
    positions = []
    for agent_id in sorted(actors_dict.keys()):
        agent = actors_dict[agent_id]
        positions.extend([agent.pose[0], agent.pose[1]])

    # 转换为numpy数组并计算哈希
    positions_array = np.array(positions)
    positions_bytes = positions_array.tobytes()
    return hashlib.md5(positions_bytes).hexdigest()


class ScenarioTracker:
    """场景追踪器，用于记录评估过程中加载的场景"""

    def __init__(self):
        self.scenario_hashes = []
        self.original_get_evaluation_scenario = None

    def patch_scenario_manager(self, scenario_manager):
        """对ScenarioManager的get_evaluation_scenario方法进行猴子补丁"""
        self.original_get_evaluation_scenario = scenario_manager.get_evaluation_scenario

        def tracked_get_evaluation_scenario():
            # 调用原始方法获取场景
            actors = self.original_get_evaluation_scenario()

            # 计算并记录场景哈希
            scenario_hash = compute_scenario_hash(actors)
            self.scenario_hashes.append(scenario_hash)
            print(f"  记录场景哈希: {scenario_hash[:8]}...")

            return actors

        # 替换原始方法
        scenario_manager.get_evaluation_scenario = tracked_get_evaluation_scenario

    def restore_scenario_manager(self, scenario_manager):
        """恢复ScenarioManager的原始方法"""
        if self.original_get_evaluation_scenario:
            scenario_manager.get_evaluation_scenario = (
                self.original_get_evaluation_scenario
            )

    def clear_records(self):
        """清空记录"""
        self.scenario_hashes = []


def setup_environment_and_agent():
    """
    设置环境和智能体，复用train_mst_ce.py中的逻辑

    Returns:
        tuple: (configs, env_fn, agent, temp_model_path)
    """
    print("正在设置环境和智能体...")

    # 注册自定义环境
    register_envs()

    # 构建配置文件路径
    config_path = os.path.join(project_root, "configs", "mappo", "ce_mappo_config.yaml")

    # 加载配置
    configs_dict = load_config(config_path)

    # 场景管理器路径处理
    if "scenario_manager" in configs_dict and configs_dict["scenario_manager"].get(
        "enabled", False
    ):
        sm_config = configs_dict["scenario_manager"]
        if not os.path.isabs(sm_config["scenarios_save_path"]):
            sm_config["scenarios_save_path"] = os.path.join(
                project_root, sm_config["scenarios_save_path"]
            )

    # 设置测试相关参数
    configs_dict["test"] = 0
    configs_dict["benchmark"] = 1
    configs_dict["test_episode"] = 10  # 确保评估10个场景
    configs_dict["evaluation_mode"] = False  # 主环境用于训练

    configs = argparse.Namespace(**configs_dict)

    # 设置随机种子
    set_seed(configs.seed)

    # 创建训练环境
    envs = make_envs(configs)
    _, _ = envs.reset()  # 重置环境但不使用返回值

    # 手动设置agents属性
    if not hasattr(envs, "agents") or not envs.agents:
        envs.agents = list(envs.observation_space.keys())

    if not hasattr(envs, "num_agents") or envs.num_agents is None:
        envs.num_agents = len(envs.agents)

    # 创建MAPPO_Agents
    agent = MAPPO_Agents(config=configs, envs=envs)

    # 创建评估环境函数
    def env_fn():
        configs_test = deepcopy(configs)
        configs_test.parallels = configs_test.test_episode
        configs_test.evaluation_mode = True  # 评估环境
        return make_envs(configs_test)

    # 保存初始模型权重到临时文件
    temp_model_dir = tempfile.mkdtemp()
    temp_model_path = os.path.join(temp_model_dir, "initial_model.pth")
    agent.save_model(temp_model_path)

    print(f"环境设置完成，智能体数量: {envs.num_agents}")
    print(f"初始模型已保存到: {temp_model_path}")

    return configs, env_fn, agent, temp_model_path


def run_evaluation_with_tracking(env_fn, agent, temp_model_path, evaluation_round):
    """
    运行一次评估并追踪场景加载

    Args:
        env_fn: 环境创建函数
        agent: MAPPO智能体
        temp_model_path: 临时模型路径
        evaluation_round: 评估轮次

    Returns:
        tuple: (scenario_hashes, average_score)
    """
    print(f"\n开始第 {evaluation_round} 轮评估...")

    # 加载固定的初始模型权重
    agent.load_model(temp_model_path)
    print("  已加载固定模型权重")

    # 创建场景追踪器
    tracker = ScenarioTracker()

    # 预先创建测试环境以获取场景管理器的引用
    test_envs = env_fn()
    scenario_manager_ref = None

    # 找到第一个环境实例并对其场景管理器进行补丁
    if hasattr(test_envs, "envs") and len(test_envs.envs) > 0:
        first_env = test_envs.envs[0]
        if hasattr(first_env, "scenario_manager") and first_env.scenario_manager:
            scenario_manager_ref = first_env.scenario_manager
            tracker.patch_scenario_manager(scenario_manager_ref)
            print("  已对场景管理器进行补丁")
    else:
        print("  警告：未找到场景管理器，无法追踪场景加载")

    try:
        # 执行评估
        print("  正在执行评估...")
        test_scores = agent.test(env_fn, 10)  # 评估10个场景
        valid_scores = test_scores[10:]  # 取后10个有效分数
        average_score = np.mean(valid_scores)

        print(f"  加载的场景数量: {len(tracker.scenario_hashes)}")
        print(f"  场景序列: {[h[:8] for h in tracker.scenario_hashes]}")
        print(f"  评估得分: {average_score:.6f}")

        return tracker.scenario_hashes, average_score

    finally:
        # 恢复原始方法
        if scenario_manager_ref:
            tracker.restore_scenario_manager(scenario_manager_ref)
            print("  已恢复场景管理器原始方法")


def main():
    """主测试函数"""
    print("=" * 60)
    print("开始验证评估场景管理机制")
    print("=" * 60)

    try:
        # 1. 设置环境和智能体
        _, env_fn, agent, temp_model_path = setup_environment_and_agent()

        # 2. 检查场景文件是否存在
        scenario_file_path = os.path.join(project_root, "scenarios", "ce_scenarios.pkl")
        print(f"\n检查场景文件: {scenario_file_path}")
        assert os.path.exists(scenario_file_path), (
            f"场景文件不存在: {scenario_file_path}"
        )
        print("✓ 场景文件存在")

        # 3. 运行3次独立评估
        all_scenario_hashes = []
        all_scores = []

        for i in range(1, 4):
            scenario_hashes, score = run_evaluation_with_tracking(
                env_fn, agent, temp_model_path, i
            )
            all_scenario_hashes.append(scenario_hashes)
            all_scores.append(score)

        # 4. 验证结果
        print("\n" + "=" * 60)
        print("验证结果")
        print("=" * 60)

        # 验证场景数量
        for i, hashes in enumerate(all_scenario_hashes):
            assert len(hashes) == 10, (
                f"第{i + 1}轮评估的场景数量不是10个，实际为{len(hashes)}个"
            )
        print("✓ 每次评估都加载了恰好10个场景")

        # 验证场景固定性
        first_sequence = all_scenario_hashes[0]
        for i in range(1, len(all_scenario_hashes)):
            assert all_scenario_hashes[i] == first_sequence, (
                f"第{i + 1}轮评估的场景序列与第1轮不同"
            )
        print("✓ 每次评估加载的场景序列完全相同")

        # 验证评估一致性
        first_score = all_scores[0]
        for i in range(1, len(all_scores)):
            assert abs(all_scores[i] - first_score) < 1e-10, (
                f"第{i + 1}轮评估得分({all_scores[i]:.6f})与第1轮({first_score:.6f})不一致"
            )
        print("✓ 使用相同权重的多次评估得分完全一致")

        print("\n" + "=" * 60)
        print("测试成功通过！评估过程具有确定性和一致性。")
        print("=" * 60)

        # 清理临时文件
        import shutil

        shutil.rmtree(os.path.dirname(temp_model_path))
        print("✓ 临时文件已清理")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        raise


def run_quick_test():
    """运行快速测试，只验证基本功能"""
    print("运行快速测试模式...")
    try:
        _, env_fn, agent, temp_model_path = setup_environment_and_agent()

        # 只运行一次评估
        scenario_hashes, score = run_evaluation_with_tracking(
            env_fn, agent, temp_model_path, 1
        )

        # 基本验证
        assert len(scenario_hashes) == 10, f"场景数量不正确: {len(scenario_hashes)}"
        assert isinstance(score, (int, float)), f"得分类型不正确: {type(score)}"

        print("✓ 快速测试通过")

        # 清理
        import shutil

        shutil.rmtree(os.path.dirname(temp_model_path))

    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        raise


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="验证评估场景管理机制")
    parser.add_argument("--quick", action="store_true", help="运行快速测试模式")
    args = parser.parse_args()

    if args.quick:
        run_quick_test()
    else:
        main()
